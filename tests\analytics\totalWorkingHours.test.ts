import { ClientAnalyticsService } from '../../src/services/clientAnalytics.service';
import { ClientAnalyticsDao } from '../../src/lib/dao/clientAnalytics.dao';

// Mock the DAO
jest.mock('../../src/lib/dao/clientAnalytics.dao');
const mockClientAnalyticsDao = ClientAnalyticsDao as jest.Mocked<typeof ClientAnalyticsDao>;

describe('Total Working Hours Analytics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return zero working hours when no completed sessions exist', async () => {
    // Arrange
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '0h 0m',
      sessionCount: 0,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') }
    });

    // Assert
    expect(mockClientAnalyticsDao.getTotalWorkingHours).toHaveBeenCalledWith(
      'therapist123',
      { from: new Date('2024-01-01'), to: new Date('2024-12-31') }
    );
    expect(result.workingHours).toBe('0h 0m');
    expect(result.sessionCount).toBe(0);
    expect(result.therapistId).toBe('therapist123');
  });

  it('should calculate total working hours from completed sessions', async () => {
    // Arrange
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '1h 30m',
      sessionCount: 2,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-06-01'), to: new Date('2024-06-02') }
    });

    // Assert
    expect(mockClientAnalyticsDao.getTotalWorkingHours).toHaveBeenCalledWith(
      'therapist123',
      { from: new Date('2024-06-01'), to: new Date('2024-06-02') }
    );
    expect(result.workingHours).toBe('1h 30m');
    expect(result.sessionCount).toBe(2);
    expect(result.therapistId).toBe('therapist123');
  });

  it('should only include completed sessions, not pending or cancelled', async () => {
    // Arrange - Mock data showing only completed sessions are counted
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '1h 0m',
      sessionCount: 1,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-06-01'), to: new Date('2024-06-03') }
    });

    // Assert
    expect(mockClientAnalyticsDao.getTotalWorkingHours).toHaveBeenCalledWith(
      'therapist123',
      { from: new Date('2024-06-01'), to: new Date('2024-06-03') }
    );
    expect(result.workingHours).toBe('1h 0m');
    expect(result.sessionCount).toBe(1);
  });

  it('should filter sessions by date range correctly', async () => {
    // Arrange - Mock data showing only sessions within date range
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '1h 0m',
      sessionCount: 1,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-06-01'), to: new Date('2024-06-30') }
    });

    // Assert
    expect(mockClientAnalyticsDao.getTotalWorkingHours).toHaveBeenCalledWith(
      'therapist123',
      { from: new Date('2024-06-01'), to: new Date('2024-06-30') }
    );
    expect(result.workingHours).toBe('1h 0m');
    expect(result.sessionCount).toBe(1);
  });

  it('should work without date range (all time)', async () => {
    // Arrange - Mock data for all time analysis
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '2h 0m',
      sessionCount: 1,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123');

    // Assert
    expect(mockClientAnalyticsDao.getTotalWorkingHours).toHaveBeenCalledWith(
      'therapist123',
      undefined
    );
    expect(result.workingHours).toBe('2h 0m');
  });

  it('should handle multiple sessions with different durations', async () => {
    // Arrange - Mock data for multiple sessions
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '3h 45m',
      sessionCount: 3,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-06-01'), to: new Date('2024-06-30') }
    });

    // Assert
    expect(result.workingHours).toBe('3h 45m');
    expect(result.sessionCount).toBe(3);
  });

  it('should return only the three required keys', async () => {
    // Arrange
    const mockData = {
      therapistId: 'therapist123',
      workingHours: '5h 0m',
      sessionCount: 5,
    };
    mockClientAnalyticsDao.getTotalWorkingHours.mockResolvedValue(mockData);

    // Act
    const result = await ClientAnalyticsService.getTotalWorkingHours('therapist123', {
      dateRange: { from: new Date('2024-06-01'), to: new Date('2024-06-30') }
    });

    // Assert
    expect(Object.keys(result)).toEqual(['therapistId', 'workingHours', 'sessionCount']);
    expect(result.therapistId).toBe('therapist123');
    expect(result.workingHours).toBe('5h 0m');
    expect(result.sessionCount).toBe(5);
  });
});
