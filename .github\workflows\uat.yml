name: Deploy ThoughtPudding Backend (UAT)

on:
  push:
    branches:
      - uat

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Get Code
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Check if package files changed
        id: deps
        run: |
          git fetch --deepen=2
          if git diff --name-only HEAD^ HEAD | grep -qE 'package(-lock)?\.json'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Start SSH Agent and add key
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      - name: Add EC2 server to known_hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.UAT_SERVER_HOST }} >> ~/.ssh/known_hosts
          chmod 644 ~/.ssh/known_hosts

      - name: Deploy to UAT Server via SSH
        run: |
          ssh ${{ secrets.SSH_USER }}@${{secrets.UAT_SERVER_HOST}} << 'EOF'
            cd therapist-backoffice-api
            git pull origin uat
            if [ "${{ steps.deps.outputs.changed }}" = "true" ]; then
              echo "Dependencies changed. Installing.."
              npm ci
            else
              echo "No dependency changes. Skipping install."
            fi
            echo "Restarting PM2 process.."
            pm2 restart ${{ secrets.PM2_APP_ID_UAT }}
            pm2 save
          EOF