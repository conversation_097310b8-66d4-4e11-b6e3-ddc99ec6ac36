import { ClientAnalyticsService } from '../../src/services/clientAnalytics.service';
import { ClientAnalyticsDao } from '../../src/lib/dao/clientAnalytics.dao';

// Mock the DAO
jest.mock('../../src/lib/dao/clientAnalytics.dao');
const mockClientAnalyticsDao = ClientAnalyticsDao as jest.Mocked<typeof ClientAnalyticsDao>;

describe('Active Clients Analytics', () => {
  const mockActiveClientsData = {
    activeClients: [
      {
        _id: '507f1f77bcf86cd799439011',
        sessionCount: 5,
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567890',
        clientId: 'TH001_1234',
        isActive: true,
        createdAt: new Date('2025-06-15')
      },
      {
        _id: '507f1f77bcf86cd799439012',
        sessionCount: 4,
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567891',
        clientId: 'TH001_5678',
        isActive: true,
        createdAt: new Date('2025-06-20')
      },
      {
        _id: '507f1f77bcf86cd799439013',
        sessionCount: 3,
        name: 'Bob Johnson',
        email: '<EMAIL>',
        phone: '+1234567892',
        clientId: 'TH001_9012',
        isActive: true,
        createdAt: new Date('2025-06-25')
      }
    ],
    totalActiveClients: 3,
    minSessionsThreshold: 3
  } as any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return active clients analysis without date range', async () => {
    // Arrange
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(mockActiveClientsData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123');

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      'therapist123',
      undefined,
      3
    );

    expect(result).toEqual({
      activeClients: mockActiveClientsData.activeClients,
      totalActiveClients: 3,
      minSessionsThreshold: 3,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should handle date range parameters', async () => {
    // Arrange
    const dateRange = {
      from: new Date('2025-06-01'),
      to: new Date('2025-06-30')
    };
    
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(mockActiveClientsData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123', { dateRange });

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      'therapist123',
      dateRange,
      3
    );
    expect(result.analysisRange).toBe('2025-06-01 to 2025-06-30');
  });

  it('should handle custom minSessions threshold', async () => {
    // Arrange
    const customThresholdData = {
      ...mockActiveClientsData,
      activeClients: mockActiveClientsData.activeClients.slice(0, 2), // Only clients with ≥5 sessions
      totalActiveClients: 2,
      minSessionsThreshold: 5
    };
    
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(customThresholdData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123', { minSessions: 5 });

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      'therapist123',
      undefined,
      5
    );
    expect(result.totalActiveClients).toBe(2);
    expect(result.minSessionsThreshold).toBe(5);
  });

  it('should handle empty data', async () => {
    // Arrange
    const emptyData = {
      activeClients: [],
      totalActiveClients: 0,
      minSessionsThreshold: 3
    };
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(emptyData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123');

    // Assert
    expect(result).toEqual({
      activeClients: [],
      totalActiveClients: 0,
      minSessionsThreshold: 3,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should work without therapist ID (global analysis)', async () => {
    // Arrange
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(mockActiveClientsData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients();

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      undefined,
      undefined,
      3
    );
    expect(result.totalActiveClients).toBe(3);
  });

  it('should handle combined date range and custom threshold', async () => {
    // Arrange
    const dateRange = {
      from: new Date('2025-07-01'),
      to: new Date('2025-07-31')
    };

    const customData = {
      ...mockActiveClientsData,
      totalActiveClients: 1,
      minSessionsThreshold: 4
    };

    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(customData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123', { 
      dateRange, 
      minSessions: 4 
    });

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      'therapist123',
      dateRange,
      4
    );
    expect(result.analysisRange).toBe('2025-07-01 to 2025-07-31');
    expect(result.minSessionsThreshold).toBe(4);
  });

  it('should return client details in the response', async () => {
    // Arrange
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(mockActiveClientsData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123');

    // Assert
    expect(result.activeClients).toHaveLength(3);
    expect(result.activeClients[0]).toEqual({
      _id: '507f1f77bcf86cd799439011',
      sessionCount: 5,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      clientId: 'TH001_1234',
      isActive: true,
      createdAt: new Date('2025-06-15')
    });
  });

  it('should default to 3 sessions threshold when not specified', async () => {
    // Arrange
    mockClientAnalyticsDao.getActiveClients.mockResolvedValue(mockActiveClientsData);

    // Act
    const result = await ClientAnalyticsService.getActiveClients('therapist123');

    // Assert
    expect(mockClientAnalyticsDao.getActiveClients).toHaveBeenCalledWith(
      'therapist123',
      undefined,
      3
    );
    expect(result.minSessionsThreshold).toBe(3);
  });
});
