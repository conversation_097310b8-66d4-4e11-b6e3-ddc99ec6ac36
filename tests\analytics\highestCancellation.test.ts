import { ClientAnalyticsService } from '../../src/services/clientAnalytics.service';
import { ClientAnalyticsDao } from '../../src/lib/dao/clientAnalytics.dao';

// Mock the DAO
jest.mock('../../src/lib/dao/clientAnalytics.dao');
const mockClientAnalyticsDao = ClientAnalyticsDao as jest.Mocked<typeof ClientAnalyticsDao>;

describe('Highest Cancellation Day Analytics', () => {
  const mockAggregatedData = [
    {
      _id: 2, // Monday (MongoDB dayOfWeek: 1=Sunday, 2=Monday, etc.)
      totalCancellations: 4
    },
    {
      _id: 5, // Thursday
      totalCancellations: 3
    },
    {
      _id: 1, // Sunday
      totalCancellations: 2
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return highest cancellation day analysis', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(mockAggregatedData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    expect(mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek).toHaveBeenCalledWith('therapist123', undefined);
    expect(result).toEqual({
      highestCancellationDays: [
        {
          dayOfWeek: 'Monday',
          dayNumber: 2,
          totalCancellations: 4,
          percentage: 44.4
        },
        {
          dayOfWeek: 'Thursday',
          dayNumber: 5,
          totalCancellations: 3,
          percentage: 33.3
        },
        {
          dayOfWeek: 'Sunday',
          dayNumber: 1,
          totalCancellations: 2,
          percentage: 22.2
        }
      ],
      totalCancellations: 9,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should handle date range parameters', async () => {
    // Arrange
    const dateRange = {
      from: new Date('2025-06-01'),
      to: new Date('2025-06-30')
    };

    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(mockAggregatedData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123', { dateRange });

    // Assert
    expect(mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek).toHaveBeenCalledWith('therapist123', dateRange);
    expect(result.analysisRange).toBe('2025-06-01 to 2025-06-30');
  });

  it('should handle empty data', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue([]);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    expect(result).toEqual({
      highestCancellationDays: [],
      totalCancellations: 0,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should calculate percentages correctly', async () => {
    // Arrange
    const testData = [
      { _id: 1, totalCancellations: 4 }, // Sunday - 40%
      { _id: 2, totalCancellations: 6 }  // Monday - 60%
    ];

    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(testData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    expect(result.highestCancellationDays).toEqual([
      { dayOfWeek: 'Monday', dayNumber: 2, totalCancellations: 6, percentage: 60.0 },
      { dayOfWeek: 'Sunday', dayNumber: 1, totalCancellations: 4, percentage: 40.0 }
    ]);
    expect(result.totalCancellations).toBe(10);
  });

  it('should handle single day data', async () => {
    // Arrange
    const singleDayData = [
      {
        _id: 3, // Tuesday
        totalCancellations: 5
      }
    ];

    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(singleDayData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    expect(result.highestCancellationDays).toHaveLength(1);
    expect(result.highestCancellationDays[0]).toEqual({
      dayOfWeek: 'Tuesday',
      dayNumber: 3,
      totalCancellations: 5,
      percentage: 100.0
    });
    expect(result.totalCancellations).toBe(5);
  });

  it('should work without therapist ID (global analysis)', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(mockAggregatedData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay();

    // Assert
    expect(mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek).toHaveBeenCalledWith(undefined, undefined);
    expect(result.totalCancellations).toBe(9);
  });

  it('should sort results by total cancellations in descending order', async () => {
    // Arrange
    const unsortedData = [
      { _id: 1, totalCancellations: 2 }, // Sunday - lowest
      { _id: 2, totalCancellations: 8 }, // Monday - highest
      { _id: 3, totalCancellations: 5 }  // Tuesday - middle
    ];

    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(unsortedData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    // Results should be sorted by totalCancellations descending: 8, 5, 2
    expect(result.highestCancellationDays[0].dayOfWeek).toBe('Monday'); // 8 cancellations
    expect(result.highestCancellationDays[1].dayOfWeek).toBe('Tuesday'); // 5 cancellations
    expect(result.highestCancellationDays[2].dayOfWeek).toBe('Sunday'); // 2 cancellations
    expect(result.totalCancellations).toBe(15);
  });

  it('should handle all days of the week correctly', async () => {
    // Arrange
    const allDaysData = [
      { _id: 1, totalCancellations: 1 }, // Sunday
      { _id: 2, totalCancellations: 2 }, // Monday
      { _id: 3, totalCancellations: 3 }, // Tuesday
      { _id: 4, totalCancellations: 4 }, // Wednesday
      { _id: 5, totalCancellations: 5 }, // Thursday
      { _id: 6, totalCancellations: 6 }, // Friday
      { _id: 7, totalCancellations: 7 }  // Saturday
    ];

    mockClientAnalyticsDao.aggregateCancellationsByDayOfWeek.mockResolvedValue(allDaysData);

    // Act
    const result = await ClientAnalyticsService.getHighestCancellationDay('therapist123');

    // Assert
    expect(result.highestCancellationDays).toHaveLength(7);
    expect(result.highestCancellationDays[0].dayOfWeek).toBe('Saturday'); // Highest (7)
    expect(result.highestCancellationDays[6].dayOfWeek).toBe('Sunday'); // Lowest (1)
    expect(result.totalCancellations).toBe(28);
  });
});