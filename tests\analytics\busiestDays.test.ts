import { ClientAnalyticsService } from '../../src/services/clientAnalytics.service';
import { ClientAnalyticsDao } from '../../src/lib/dao/clientAnalytics.dao';
import { ScheduleStatus } from '../../src/models/Schedule.model';

// Mock the DAO
jest.mock('../../src/lib/dao/clientAnalytics.dao');
const mockClientAnalyticsDao = ClientAnalyticsDao as jest.Mocked<typeof ClientAnalyticsDao>;

describe('Busiest Days Analytics', () => {
  const mockSessionData = [
    {
      _id: 6, // Friday (MongoDB dayOfWeek: 1=Sunday, 2=Monday, etc.)
      sessionCount: 12
    },
    {
      _id: 7, // Saturday
      sessionCount: 8
    },
    {
      _id: 2, // Monday
      sessionCount: 6
    },
    {
      _id: 3, // Tuesday
      sessionCount: 4
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return busiest days analysis without date range', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(mockSessionData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123');

    // Assert
    expect(mockClientAnalyticsDao.aggregateSessionsByDayOfWeek).toHaveBeenCalledWith(
      'therapist123',
      undefined,
      [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED]
    );
    expect(result).toEqual({
      busiestDays: [
        {
          dayOfWeek: 'Friday',
          dayNumber: 6,
          sessionCount: 12,
          percentage: 40.0
        },
        {
          dayOfWeek: 'Saturday',
          dayNumber: 7,
          sessionCount: 8,
          percentage: 26.7
        },
        {
          dayOfWeek: 'Monday',
          dayNumber: 2,
          sessionCount: 6,
          percentage: 20.0
        },
        {
          dayOfWeek: 'Tuesday',
          dayNumber: 3,
          sessionCount: 4,
          percentage: 13.3
        }
      ],
      totalSessions: 30,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should handle date range parameters', async () => {
    // Arrange
    const dateRange = {
      from: new Date('2025-06-01'),
      to: new Date('2025-06-30')
    };
    
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(mockSessionData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123', { dateRange });

    // Assert
    expect(mockClientAnalyticsDao.aggregateSessionsByDayOfWeek).toHaveBeenCalledWith(
      'therapist123',
      dateRange,
      [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED]
    );
    expect(result.analysisRange).toBe('2025-06-01 to 2025-06-30');
  });

  it('should handle empty data', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue([]);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123');

    // Assert
    expect(result).toEqual({
      busiestDays: [],
      totalSessions: 0,
      analysisRange: 'All time',
      generatedAt: expect.any(Date)
    });
  });

  it('should calculate percentages correctly', async () => {
    // Arrange
    const testData = [
      { _id: 1, sessionCount: 20 }, // Sunday - 66.7%
      { _id: 2, sessionCount: 10 }  // Monday - 33.3%
    ];
    
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(testData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123');

    // Assert
    expect(result.busiestDays).toEqual([
      { dayOfWeek: 'Sunday', dayNumber: 1, sessionCount: 20, percentage: 66.7 },
      { dayOfWeek: 'Monday', dayNumber: 2, sessionCount: 10, percentage: 33.3 }
    ]);
    expect(result.totalSessions).toBe(30);
  });

  it('should handle single day data', async () => {
    // Arrange
    const singleDayData = [
      {
        _id: 3, // Tuesday
        sessionCount: 15
      }
    ];
    
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(singleDayData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123');

    // Assert
    expect(result.busiestDays).toHaveLength(1);
    expect(result.busiestDays[0]).toEqual({
      dayOfWeek: 'Tuesday',
      dayNumber: 3,
      sessionCount: 15,
      percentage: 100.0
    });
    expect(result.totalSessions).toBe(15);
  });

  it('should work without therapist ID (global analysis)', async () => {
    // Arrange
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(mockSessionData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek();

    // Assert
    expect(mockClientAnalyticsDao.aggregateSessionsByDayOfWeek).toHaveBeenCalledWith(
      undefined,
      undefined,
      [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED]
    );
    expect(result.totalSessions).toBe(30);
  });

  it('should handle custom schedule statuses', async () => {
    // Arrange
    const customStatuses = [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED];
    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(mockSessionData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123', {
      includeStatuses: customStatuses
    });

    // Assert
    expect(mockClientAnalyticsDao.aggregateSessionsByDayOfWeek).toHaveBeenCalledWith(
      'therapist123',
      undefined,
      customStatuses
    );
  });

  it('should handle combined options correctly', async () => {
    // Arrange
    const dateRange = {
      from: new Date('2025-06-01'),
      to: new Date('2025-06-30')
    };
    const customStatuses = [ScheduleStatus.CONFIRMED];

    mockClientAnalyticsDao.aggregateSessionsByDayOfWeek.mockResolvedValue(mockSessionData);

    // Act
    const result = await ClientAnalyticsService.getBusiestDaysOfWeek('therapist123', {
      dateRange,
      includeStatuses: customStatuses
    });

    // Assert
    expect(mockClientAnalyticsDao.aggregateSessionsByDayOfWeek).toHaveBeenCalledWith(
      'therapist123',
      dateRange,
      customStatuses
    );
    expect(result.analysisRange).toBe('2025-06-01 to 2025-06-30');
  });
});