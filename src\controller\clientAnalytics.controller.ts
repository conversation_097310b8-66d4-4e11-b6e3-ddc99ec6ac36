import express from "express";
import { ClientAnalyticsService } from "../services/clientAnalytics.service";
import { getErrorResponse } from "../helper/error.handler";

export class ClientAnalyticsController {
  /**
   * Get highest cancellation day of the week analysis
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   */
  static async getHighestCancellationDay(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;

      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined,
      };

      const analysis = await ClientAnalyticsService.getHighestCancellationDay(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Highest cancellation day analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }

    /**
   * Get busiest days of the week analysis
   * Includes CONFIRMED, COMPLETED, and RESCHEDULED sessions
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   */
   static async getBusiestDaysOfWeek(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;

      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined
      };

      const analysis = await ClientAnalyticsService.getBusiestDaysOfWeek(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Busiest days analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }



  // Ongoing Session Price Avg. - Across all ACTIVE clients in the DB
  static async getSessionPriceAvg(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate as string;
      const toDate = req.query.toDate as string;
      
      // validate date inputs if provided
      if (!fromDate || !toDate) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "fromDate and toDate are required."
            )
          );
      }

      // getting the average session price for the therapist
      const averageSessionPriceData = await ClientAnalyticsService.getSessionPriceAvg(
        therapistId,
        fromDate,
        toDate
      );
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Average session price fetched successfully",
        data: averageSessionPriceData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get active clients analytics (clients with ≥3 sessions in specified period)
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   * - minSessions (optional): Minimum sessions threshold (default: 3)
   */
  static async getActiveClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;
      const minSessions = req.query.minSessions ? parseInt(req.query.minSessions as string) : 3;
      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }
      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }
      // Validate minSessions parameter
      if (isNaN(minSessions) || minSessions < 1) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "minSessions must be a positive integer"
            )
          );
      }
      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined,
        minSessions: minSessions,
      };
      const analysis = await ClientAnalyticsService.getActiveClients(therapistId, options);
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Active clients analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get power clients
   * @param req 
   * @param res 
   * @param next
   */
  static async getPowerClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate as string;
      const toDate = req.query.toDate as string;

      // validate date inputs if provided
      if (!fromDate || !toDate) {
        return res
          .status(400)
          .send(getErrorResponse("VALIDATION_ERROR", "fromDate and toDate are required."));
      }

      const powerClientsData = await ClientAnalyticsService.getPowerClients(therapistId, fromDate, toDate);

      let finalData = {};
      if (powerClientsData && powerClientsData.length !== 0) {
        finalData = {
          totalClients: powerClientsData[0].totalClients,
          powerClientsCount: powerClientsData.length,
          powerClientsData: powerClientsData.map((client: any) => {
            const { totalClients, ...clientWithoutTotal } = client;
            return clientWithoutTotal;
          }),
        }
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Power clients fetched successfully",
        data: finalData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get total working hours analytics from completed sessions
   */
  static async getTotalWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { fromDate, toDate } = req.query;

      // Parse and validate dates
      const fromDateObj = fromDate ? new Date(fromDate as string) : undefined;
      const toDateObj = toDate ? new Date(toDate as string) : undefined;

      if (fromDate && isNaN(fromDateObj!.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDateObj!.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDateObj && toDateObj ? { from: fromDateObj, to: toDateObj } : undefined,
      };

      const analysis = await ClientAnalyticsService.getTotalWorkingHours(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Total working hours analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get risk clients analytics (clients with bad session management or payment history)
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   */
  static async getRiskClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;

      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined,
      };

      const analysis = await ClientAnalyticsService.getRiskClients(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Risk clients analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get busiest 3-hour time slots analytics
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   */
  static async getBusiestTimeSlots(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;

      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined,
      };

      const analysis = await ClientAnalyticsService.getBusiestTimeSlots(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Busiest time slots analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get payment summary analytics from PayTracker collection
   * Query parameters:
   * - fromDate (optional): Start date for analysis (YYYY-MM-DD)
   * - toDate (optional): End date for analysis (YYYY-MM-DD)
   */
  static async getPaymentSummary(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const fromDate = req.query.fromDate ? new Date(req.query.fromDate as string) : undefined;
      const toDate = req.query.toDate ? new Date(req.query.toDate as string) : undefined;

      // Validate date inputs if provided
      if (fromDate && isNaN(fromDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid fromDate format. Use YYYY-MM-DD"
            )
          );
      }

      if (toDate && isNaN(toDate.getTime())) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid toDate format. Use YYYY-MM-DD"
            )
          );
      }

      const options = {
        dateRange: fromDate && toDate ? { from: fromDate, to: toDate } : undefined,
      };

      const analysis = await ClientAnalyticsService.getPaymentSummary(therapistId, options);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Payment summary analysis fetched successfully",
        data: analysis,
      });
    } catch (error) {
      next(error);
    }
  }
}