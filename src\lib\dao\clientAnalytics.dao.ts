import mongoose from "mongoose";
import ScheduleModel, { ScheduleStatus } from "../../models/Schedule.model";
import ClientModel from "../../models/Client.model";
import PayTrackerModel, { PaymentTrackerStatusEnum, PaymentTrackerTypeEnum } from "../../models/PayTracker.model";

export class ClientAnalyticsDao {
  /**
   * Aggregate cancellations by day of week for highest cancellation day analysis
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @returns Aggregated cancellation data grouped by day of week
   */
  static async aggregateCancellationsByDayOfWeek(
    therapistId?: string,
    dateRange?: { from: Date; to: Date }
  ) {
    const matchConditions: any = {};

    // Add therapist filter if provided
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }

    // Build aggregation pipeline for cancelled sessions
    const pipeline: any[] = [
      { $match: matchConditions },
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          "recurrenceDates.status": ScheduleStatus.CANCELLED,
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },
      {
        $addFields: {
          dayOfWeek: { $dayOfWeek: "$recurrenceDates.fromDate" }, // 1=Sunday, 7=Saturday
        },
      },
      {
        $group: {
          _id: "$dayOfWeek",
          totalCancellations: { $sum: 1 },
          cancellations: { $push: "$recurrenceDates" },
        },
      },
      { $sort: { totalCancellations: -1 } }, // Sort by total cancellations descending
    ];

    return await ScheduleModel.aggregate(pipeline);
  }

   /**
   * Aggregate sessions by day of week for busiest days analysis
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @param statuses - Array of schedule statuses to include
   * @returns Aggregated data grouped by day of week
   */
  static async aggregateSessionsByDayOfWeek(
    therapistId?: string,
    dateRange?: { from: Date; to: Date },
    statuses: ScheduleStatus[] = [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED]
  ) {
    const matchConditions: any = {};

    // Add therapist filter if provided
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }

    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: matchConditions },
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          "recurrenceDates.status": { $in: statuses },
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },
      {
        $addFields: {
          dayOfWeek: { $dayOfWeek: "$recurrenceDates.fromDate" }, // 1=Sunday, 7=Saturday
        },
      },
      {
        $group: {
          _id: "$dayOfWeek",
          sessionCount: { $sum: 1 },
          sessions: { $push: "$recurrenceDates" },
        },
      },
      { $sort: { sessionCount: -1 } }, // Sort by session count descending
    ];

    return await ScheduleModel.aggregate(pipeline);
  }



  static async getSessionPriceAvg(therapistId: string, fromDate: string, toDate: string) {
    const pipeline: any[] = [
      { $match: { therapistId: new mongoose.Types.ObjectId(therapistId) } },
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          "recurrenceDates.fromDate": { $gte: new Date(fromDate), $lte: new Date(toDate) },
          // "recurrenceDates.status": { $in: ["confirmed", "rescheduled"] }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: "$recurrenceDates.amount" },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          averageAmount: {
            $cond: [
              { $eq: ["$count", 0] },
              0,
              { $round: [ { $divide: ["$totalAmount", "$count"] }, 2 ] }
            ]
          },
          count: 1
        }
      }
    ];
    const result = await ScheduleModel.aggregate(pipeline);
    return result[0] || { averageAmount: 0, count: 0 };
  }

  /**
   * Get active clients with ≥3 sessions in specified date range
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering sessions
   * @param minSessions - Minimum number of sessions to consider client as active (default: 3)
   * @returns Active clients with session counts and details
   */
  static async getActiveClients(
    therapistId?: string,
    dateRange?: { from: Date; to: Date },
    minSessions: number = 3
  ) {
    const matchConditions: any = {};
    // Add therapist filter if provided
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }
    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: matchConditions },
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          "recurrenceDates.status": {
            $in: [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED]
          },
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },
      {
        $group: {
          _id: "$clientId",
          sessionCount: { $sum: 1 },
          sessions: { $push: "$recurrenceDates" },
          // Get client details from the first document
          email: { $first: "$email" },
          name: { $first: "$name" },
          phone: { $first: "$phone" },
        },
      },
      {
        $match: {
          sessionCount: { $gte: minSessions }
        }
      },
      {
        $lookup: {
          from: "client",
          localField: "_id",
          foreignField: "_id",
          as: "clientDetails"
        }
      },
      {
        $addFields: {
          clientDetails: { $arrayElemAt: ["$clientDetails", 0] }
        }
      },
      {
        $project: {
          _id: 1,
          sessionCount: 1,
          email: { $ifNull: ["$clientDetails.email", "$email"] },
          name: { $ifNull: ["$clientDetails.name", "$name"] },
          isActive: { $ifNull: ["$clientDetails.isActive", true] },
        }
      },
      { $sort: { sessionCount: -1 } }, // Sort by session count descending
    ];
    const results = await ScheduleModel.aggregate(pipeline);
    return {
      activeClients: results,
      totalActiveClients: results.length,
      minSessionsThreshold: minSessions
    };
  }

  /**
   * Get total working hours analytics from completed sessions
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @returns Total working hours and therapist details
   */
  static async getTotalWorkingHours(
    therapistId?: string,
    dateRange?: { from: Date; to: Date }
  ) {
    const matchConditions: any = {};

    // Add therapist filter if provided
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }

    const pipeline = [
      // 1. Match schedules for the therapist
      { $match: matchConditions },

      // 2. Unwind recurrenceDates to work with individual sessions
      { $unwind: "$recurrenceDates" },

      // 3. Filter completed sessions within date range
      {
        $match: {
          "recurrenceDates.status": ScheduleStatus.COMPLETED,
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },

      // 4. Calculate session duration in minutes
      {
        $addFields: {
          sessionDurationMinutes: {
            $divide: [
              { $subtract: ["$recurrenceDates.toDate", "$recurrenceDates.fromDate"] },
              1000 * 60, // Convert milliseconds to minutes
            ],
          },
        },
      },

      // 5. Group by therapistId to calculate total working hours
      {
        $group: {
          _id: "$therapistId",
          totalMinutes: { $sum: "$sessionDurationMinutes" },
          totalSessions: { $sum: 1 },
        },
      },

      // 6. Convert minutes to hours and format
      {
        $addFields: {
          totalWorkingHours: { $divide: ["$totalMinutes", 60] },
          totalWorkingHoursFormatted: {
            $concat: [
              { $toString: { $floor: { $divide: ["$totalMinutes", 60] } } },
              "h ",
              { $toString: { $mod: [{ $round: "$totalMinutes" }, 60] } },
              "m",
            ],
          },
        },
      },

      // 7. Project final fields
      {
        $project: {
          therapistId: "$_id",
          workingHours: "$totalWorkingHoursFormatted",
          sessionCount: "$totalSessions",
          _id: 0,
        },
      },
    ];

    const results = await ScheduleModel.aggregate(pipeline);

    // If no results, return zero values
    if (results.length === 0) {
      return {
        therapistId: therapistId || null,
        workingHours: "0h 0m",
        sessionCount: 0,
      };
    }

    return results[0];
  }

  static async getPowerClients(
    therapistId: string,
    fromDate: string,
    toDate: string
  ) {
    const pipeline: any[] = [
      // 1. Match by therapistId and date range
      { $match: { therapistId: new mongoose.Types.ObjectId(therapistId) } },

      // 2. Unwind recurrenceDates
      { $unwind: "$recurrenceDates" },

      // 3. Match recurrenceDates.fromDate within range
      {
        $match: {
          "recurrenceDates.fromDate": {
            $gte: new Date(fromDate),
            $lte: new Date(toDate),
          },
        },
      },

      // 4. Lookup paytracker
      {
        $lookup: {
          from: "paytracker",
          localField: "recurrenceDates.payTrackerId",
          foreignField: "_id",
          as: "paytrackers",
        },
      },

      // 5. Unwind paytrackers
      { $unwind: "$paytrackers" },

      // 6. Filter to relevant paid statuses
      {
        $match: {
          "paytrackers.status": {
            $in: ["Paid on time", "Paid Delay", "Paid Cancellation"],
          },
        },
      },

      // 7. Project clientId and amount
      {
        $project: {
          clientId: "$clientId",
          amount: {
            $switch: {
              branches: [
                {
                  case: {
                    $in: [
                      "$paytrackers.status",
                      ["Paid on time", "Paid Delay"],
                    ],
                  },
                  then: "$paytrackers.amount.value",
                },
                {
                  case: { $eq: ["$paytrackers.status", "Paid Cancellation"] },
                  then: "$paytrackers.cancellationFee.value",
                },
              ],
              default: 0,
            },
          },
        },
      },

      // 8. Group revenue by client
      {
        $group: {
          _id: "$clientId",
          totalRevenue: { $sum: "$amount" },
        },
      },

      // 9. Sort by totalRevenue descending
      { $sort: { totalRevenue: -1 } },

      // 10. Add total client count and rank
      {
        $setWindowFields: {
          partitionBy: null,
          sortBy: { totalRevenue: -1 },
          output: {
            totalClients: { $count: {} },
            rank: { $documentNumber: {} },
          },
        },
      },

      // 11. Calculate 70% cut-off
      {
        $addFields: {
          seventyPercentCount: {
            $ceil: { $multiply: ["$totalClients", 0.7] },
          },
        },
      },

      // 12. Filter top 70% clients
      {
        $match: {
          $expr: { $lte: ["$rank", "$seventyPercentCount"] },
        },
      },

      // 13. Cast clientId to ObjectId if needed
      {
        $addFields: {
          clientIdObj: {
            $cond: [
              { $eq: [{ $type: "$_id" }, "string"] },
              { $toObjectId: "$_id" },
              "$_id",
            ],
          },
        },
      },

      // 14. Lookup client details
      {
        $lookup: {
          from: "client",
          localField: "clientIdObj",
          foreignField: "_id",
          as: "clientDetails",
        },
      },
      { $unwind: "$clientDetails" },

      // 15. Final projection
      {
        $project: {
          _id: 0,
          clientId: "$clientIdObj",
          name: "$clientDetails.name",
          email: "$clientDetails.email",
          totalRevenue: 1,
          totalClients: 1,
        },
      },
    ];
    return await ScheduleModel.aggregate(pipeline);
  }

  /**
   * Get risk clients with bad session management or payment history
   * Logic:
   * 1. Check if client has any completed session in the past
   * 2. Filter schedules using fromDate and toDate on session date
   * 3. Check if client has more than 2 sessions in the date interval
   * 4. Mark as risk client if Rescheduled/Cancelled/Paid Delay/Paid Cancellation % > 50%
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @returns Array of risk clients with their issues
   */
  static async getRiskClients(
    therapistId?: string,
    dateRange?: { from: Date; to: Date }
  ) {
    const matchConditions: any = {};
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }

    // Build aggregation pipeline for risk clients analysis
    const pipeline: any[] = [
      // Step 1: Match therapist
      { $match: matchConditions },

      // Step 2: Check if client has any completed sessions in the past (all-time)
      {
        $lookup: {
          from: "schedules",
          let: { clientId: "$clientId" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$clientId", "$$clientId"] },
                ...(therapistId && {
                  therapistId: new mongoose.Types.ObjectId(therapistId),
                }),
              },
            },
            { $unwind: "$recurrenceDates" },
            {
              $match: {
                "recurrenceDates.status": ScheduleStatus.COMPLETED,
              },
            },
            {
              $count: "completedCount"
            },
          ],
          as: "completedSessionsCheck",
        },
      },

      // Step 3: Filter out clients with no completed sessions in the past
      {
        $match: {
          "completedSessionsCheck.0.completedCount": { $gt: 0 }
        },
      },

      // Step 4: Unwind and filter by date range
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },
      // Step 5: Group by client and count sessions/issues in date range
      {
        $group: {
          _id: "$clientId",
          totalSessions: { $sum: 1 }, // Sessions in date range
          cancelledSessions: {
            $sum: {
              $cond: [
                { $eq: ["$recurrenceDates.status", ScheduleStatus.CANCELLED] },
                1,
                0,
              ],
            },
          },
          rescheduledSessions: {
            $sum: {
              $cond: [
                { $eq: ["$recurrenceDates.status", ScheduleStatus.RESCHEDULED] },
                1,
                0,
              ],
            },
          },
          // Get client details from the first document
          email: { $first: "$email" },
          name: { $first: "$name" },
          phone: { $first: "$phone" },
          sessions: { $push: "$recurrenceDates" },
        },
      },

      // Step 6: Filter clients with more than 2 sessions in date interval
      {
        $match: {
          totalSessions: { $gt: 2 }
        },
      },

      {
        $lookup: {
          from: "client",
          localField: "_id",
          foreignField: "_id",
          as: "clientDetails",
        },
      },
      { $unwind: "$clientDetails" },
      {
        $lookup: {
          from: "paytracker",
          let: { clientId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$clientId", "$$clientId"] },
                ...(therapistId && {
                  therapistId: new mongoose.Types.ObjectId(therapistId),
                }),
                ...(dateRange && {
                  dueDate: {
                    $gte: dateRange.from,
                    $lte: dateRange.to,
                  },
                }),
              },
            },
          ],
          as: "paymentHistory",
        },
      },

      {
        $addFields: {
          totalPayments: { $size: "$paymentHistory" },
          delayedPayments: {
            $size: {
              $filter: {
                input: "$paymentHistory",
                cond: { $eq: ["$$this.status", "Paid Delay"] },
              },
            },
          },
          cancelledPayments: {
            $size: {
              $filter: {
                input: "$paymentHistory",
                cond: {
                  $in: ["$$this.status", ["Paid Cancellation", "Free Cancellation"]],
                },
              },
            },
          },
          // Calculate percentages: (issues in date range) / (total sessions in same date range)
          cancellationPercentage: {
            $cond: [
              { $gt: ["$totalSessions", 0] },
              { $multiply: [{ $divide: ["$cancelledSessions", "$totalSessions"] }, 100] },
              0,
            ],
          },
          reschedulePercentage: {
            $cond: [
              { $gt: ["$totalSessions", 0] },
              { $multiply: [{ $divide: ["$rescheduledSessions", "$totalSessions"] }, 100] },
              0,
            ],
          },
          // Calculate payment percentages based on total sessions in date range
          delayedPaymentPercentage: {
            $cond: [
              { $gt: ["$totalSessions", 0] },
              { $multiply: [{ $divide: ["$delayedPayments", "$totalSessions"] }, 100] },
              0,
            ],
          },
          cancelledPaymentPercentage: {
            $cond: [
              { $gt: ["$totalSessions", 0] },
              { $multiply: [{ $divide: ["$cancelledPayments", "$totalSessions"] }, 100] },
              0,
            ],
          },
        },
      },
      // Step 7: Apply risk criteria - mark as risk client if any category > 50%
      {
        $addFields: {
          // Check which categories meet the >50% threshold
          isRescheduleRisk: { $gt: ["$reschedulePercentage", 50] },
          isCancellationRisk: { $gt: ["$cancellationPercentage", 50] },
          isDelayedPaymentRisk: { $gt: ["$delayedPaymentPercentage", 50] },
          isCancelledPaymentRisk: { $gt: ["$cancelledPaymentPercentage", 50] },

          // Find the highest percentage among the 4 categories
          maxPercentage: {
            $max: [
              "$cancellationPercentage",
              "$reschedulePercentage",
              "$delayedPaymentPercentage",
              "$cancelledPaymentPercentage"
            ]
          },

          // Determine primary risk tag based on highest percentage (only if >50%)
          primaryRiskTag: {
            $switch: {
              branches: [
                {
                  case: {
                    $and: [
                      { $eq: ["$maxPercentage", "$reschedulePercentage"] },
                      { $gt: ["$reschedulePercentage", 50] }
                    ]
                  },
                  then: "Most Reschedules"
                },
                {
                  case: {
                    $and: [
                      { $eq: ["$maxPercentage", "$cancellationPercentage"] },
                      { $gt: ["$cancellationPercentage", 50] }
                    ]
                  },
                  then: "Most Cancellations"
                },
                {
                  case: {
                    $and: [
                      { $eq: ["$maxPercentage", "$delayedPaymentPercentage"] },
                      { $gt: ["$delayedPaymentPercentage", 50] }
                    ]
                  },
                  then: "Most Delayed Payments"
                },
                {
                  case: {
                    $and: [
                      { $eq: ["$maxPercentage", "$cancelledPaymentPercentage"] },
                      { $gt: ["$cancelledPaymentPercentage", 50] }
                    ]
                  },
                  then: "Most Cancelled Payments"
                }
              ],
              default: null
            }
          },

          // Check if client qualifies as risk client (any category > 50%)
          isRiskClient: {
            $or: [
              { $gt: ["$reschedulePercentage", 50] },
              { $gt: ["$cancellationPercentage", 50] },
              { $gt: ["$delayedPaymentPercentage", 50] },
              { $gt: ["$cancelledPaymentPercentage", 50] }
            ]
          }
        },
      },

      // Step 8: Filter - Only include risk clients (any category > 50%)
      {
        $match: {
          isRiskClient: true
        },
      },
      {
        $project: {
          _id: 0,
          clientId: "$_id",
          name: "$clientDetails.name",
          email: "$clientDetails.email",
          phone: "$clientDetails.phone",
          isActive: "$clientDetails.isActive",
          totalSessions: 1, // Total sessions in date range
          cancelledSessions: 1, // Cancelled sessions in date range
          rescheduledSessions: 1, // Rescheduled sessions in date range
          totalPayments: 1,
          delayedPayments: 1,
          cancelledPayments: 1,
          reschedulePercentage: { $round: ["$reschedulePercentage", 2] },
          cancellationPercentage: { $round: ["$cancellationPercentage", 2] },
          delayedPaymentPercentage: { $round: ["$delayedPaymentPercentage", 2] },
          cancelledPaymentPercentage: { $round: ["$cancelledPaymentPercentage", 2] },
          maxPercentage: { $round: ["$maxPercentage", 2] },
          // Risk flags for each category (>50%)
          isRescheduleRisk: 1,
          isCancellationRisk: 1,
          isDelayedPaymentRisk: 1,
          isCancelledPaymentRisk: 1,
        },
      },
      { $sort: { maxPercentage: -1, primaryRiskTag: 1 } },
    ];

    return await ScheduleModel.aggregate(pipeline);
  }

  /**
   * Get busiest 3-hour time slots based on session occupancy
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @returns Array of 3-hour time slots with session counts, sorted by occupancy
   */
  static async getBusiestTimeSlots(
    therapistId?: string,
    dateRange?: { from: Date; to: Date }
  ) {
    const matchConditions: any = {};
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }

    // Build aggregation pipeline for busiest time slots analysis
    const pipeline: any[] = [
      { $match: matchConditions },
      { $unwind: "$recurrenceDates" },
      {
        $match: {
          "recurrenceDates.status": {
            $in: [ScheduleStatus.COMPLETED]
          },
          ...(dateRange && {
            "recurrenceDates.fromDate": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }),
        },
      },
      {
        $addFields: {
          // Extract hour from session start time
          sessionHour: { $hour: "$recurrenceDates.fromDate" },
        },
      },
      {
        $addFields: {
          // Determine which 3-hour slot this session belongs to
          timeSlot: {
            $switch: {
              branches: [
                { case: { $and: [{ $gte: ["$sessionHour", 0] }, { $lt: ["$sessionHour", 3] }] }, then: "12:00 AM - 3:00 AM" },
                { case: { $and: [{ $gte: ["$sessionHour", 3] }, { $lt: ["$sessionHour", 6] }] }, then: "3:00 AM - 6:00 AM" },
                { case: { $and: [{ $gte: ["$sessionHour", 6] }, { $lt: ["$sessionHour", 9] }] }, then: "6:00 AM - 9:00 AM" },
                { case: { $and: [{ $gte: ["$sessionHour", 9] }, { $lt: ["$sessionHour", 12] }] }, then: "9:00 AM - 12:00 PM" },
                { case: { $and: [{ $gte: ["$sessionHour", 12] }, { $lt: ["$sessionHour", 15] }] }, then: "12:00 PM - 3:00 PM" },
                { case: { $and: [{ $gte: ["$sessionHour", 15] }, { $lt: ["$sessionHour", 18] }] }, then: "3:00 PM - 6:00 PM" },
                { case: { $and: [{ $gte: ["$sessionHour", 18] }, { $lt: ["$sessionHour", 21] }] }, then: "6:00 PM - 9:00 PM" },
                { case: { $and: [{ $gte: ["$sessionHour", 21] }, { $lt: ["$sessionHour", 24] }] }, then: "9:00 PM - 12:00 AM" }
              ],
              default: "Unknown"
            }
          },
          // Store the start hour for sorting purposes
          slotStartHour: {
            $switch: {
              branches: [
                { case: { $and: [{ $gte: ["$sessionHour", 0] }, { $lt: ["$sessionHour", 3] }] }, then: 0 },
                { case: { $and: [{ $gte: ["$sessionHour", 3] }, { $lt: ["$sessionHour", 6] }] }, then: 3 },
                { case: { $and: [{ $gte: ["$sessionHour", 6] }, { $lt: ["$sessionHour", 9] }] }, then: 6 },
                { case: { $and: [{ $gte: ["$sessionHour", 9] }, { $lt: ["$sessionHour", 12] }] }, then: 9 },
                { case: { $and: [{ $gte: ["$sessionHour", 12] }, { $lt: ["$sessionHour", 15] }] }, then: 12 },
                { case: { $and: [{ $gte: ["$sessionHour", 15] }, { $lt: ["$sessionHour", 18] }] }, then: 15 },
                { case: { $and: [{ $gte: ["$sessionHour", 18] }, { $lt: ["$sessionHour", 21] }] }, then: 18 },
                { case: { $and: [{ $gte: ["$sessionHour", 21] }, { $lt: ["$sessionHour", 24] }] }, then: 21 }
              ],
              default: 0
            }
          }
        },
      },
      {
        $group: {
          _id: "$timeSlot",
          sessionCount: { $sum: 1 },
          slotStartHour: { $first: "$slotStartHour" },
          sessions: { $push: "$recurrenceDates" },
        },
      },
      {
        $sort: {
          sessionCount: -1,  // Sort by session count (highest first)
          slotStartHour: -1  // If tied, show later slots first
        }
      },
      {
        $addFields: {
          rank: { $literal: 1 } // Will be updated in post-processing
        }
      },
      {
        $project: {
          _id: 0,
          timeSlot: "$_id",
          sessionCount: 1,
          slotStartHour: 1,
          rank: 1,
        },
      }
    ];

    const results = await ScheduleModel.aggregate(pipeline);

    // Post-process to handle ranking and filtering logic
    if (results.length === 0) {
      return [];
    }

    // Find the highest session count
    const maxCount = results[0].sessionCount;

    // Get all slots with the highest count
    const topSlots = results.filter(slot => slot.sessionCount === maxCount);

    // If there are more than 2 slots with the same highest count, return the latest 2
    if (topSlots.length > 2) {
      return topSlots
        .sort((a, b) => b.slotStartHour - a.slotStartHour) // Sort by start hour descending
        .slice(0, 2) // Take the latest 2
        .map((slot, index) => ({ ...slot, rank: index + 1 }));
    }

    // If 2 or fewer slots have the highest count, return all of them
    return topSlots.map((slot, index) => ({ ...slot, rank: index + 1 }));
  }

  /**
   * Get payment summary from PayTracker collection within specified datetime range
   * @param therapistId - Optional therapist ID for filtering
   * @param dateRange - Optional date range for filtering
   * @returns Payment summary with amounts by type and status
   */
  static async getPaymentSummary(
    therapistId?: string,
    dateRange?: { from: Date; to: Date }
  ) {
    const matchConditions: any = {};
    if (therapistId) {
      matchConditions.therapistId = new mongoose.Types.ObjectId(therapistId);
    }
    if (dateRange) {
      matchConditions.dueDate = {
        $gte: dateRange.from,
        $lte: dateRange.to,
      };
    }

    // Build aggregation pipeline for payment summary with schedule lookup
    const pipeline: any[] = [
      { $match: matchConditions },

      // Lookup schedule data to get session dates
      {
        $lookup: {
          from: "schedules",
          localField: "scheduleId",
          foreignField: "_id",
          as: "schedule"
        }
      },
      { $unwind: "$schedule" },

      // Add field to get the session date from recurrenceDates
      {
        $addFields: {
          sessionDate: {
            $let: {
              vars: {
                matchingRecurrence: {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: "$schedule.recurrenceDates",
                        cond: { $eq: ["$$this._id", "$scheduleRecId"] }
                      }
                    },
                    0
                  ]
                }
              },
              in: "$$matchingRecurrence.fromDate"
            }
          }
        }
      },

      {
        $group: {
          _id: null,
          // 1. Received after sessions: Status in [Paid_On_Time, Paid_Delayed] AND paymentDate > sessionDate
          receivedAfterSessionsAmount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    {
                      $in: [
                        "$status",
                        [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                      ]
                    },
                    { $gt: ["$paymentDate", "$sessionDate"] }
                  ]
                },
                "$amount.value",
                0
              ]
            }
          },
          receivedAfterSessionsCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    {
                      $in: [
                        "$status",
                        [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                      ]
                    },
                    { $gt: ["$paymentDate", "$sessionDate"] }
                  ]
                },
                1,
                0
              ]
            }
          },

          // 2. Cancellation Fees: status = Paid Cancellation, sum cancellationFee.value
          cancellationFeesAmount: {
            $sum: {
              $cond: [
                { $eq: ["$status", PaymentTrackerStatusEnum.Cancelled_Paid_Fee] },
                "$cancellationFee.value",
                0
              ]
            }
          },
          cancellationFeesCount: {
            $sum: {
              $cond: [
                { $eq: ["$status", PaymentTrackerStatusEnum.Cancelled_Paid_Fee] },
                1,
                0
              ]
            }
          },

          // 3. Advance payments: Status in [Paid_On_Time, Paid_Delayed] AND paymentDate < sessionDate
          advancePaymentAmount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    {
                      $in: [
                        "$status",
                        [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                      ]
                    },
                    { $lt: ["$paymentDate", "$sessionDate"] }
                  ]
                },
                "$amount.value",
                0
              ]
            }
          },
          advancePaymentCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    {
                      $in: [
                        "$status",
                        [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                      ]
                    },
                    { $lt: ["$paymentDate", "$sessionDate"] }
                  ]
                },
                1,
                0
              ]
            }
          },

          // Total calculations - sum of all payment categories
          totalAmount: {
            $sum: {
              $cond: [
                {
                  $or: [
                    // Payments received after session date
                    {
                      $and: [
                        {
                          $in: [
                            "$status",
                            [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                          ]
                        },
                        { $gt: ["$paymentDate", "$sessionDate"] }
                      ]
                    },
                    // Payments received before session date (advance)
                    {
                      $and: [
                        {
                          $in: [
                            "$status",
                            [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                          ]
                        },
                        { $lt: ["$paymentDate", "$sessionDate"] }
                      ]
                    },
                    // Cancellation fees
                    { $eq: ["$status", PaymentTrackerStatusEnum.Cancelled_Paid_Fee] }
                  ]
                },
                {
                  $cond: [
                    { $eq: ["$status", PaymentTrackerStatusEnum.Cancelled_Paid_Fee] },
                    "$cancellationFee.value",
                    "$amount.value"
                  ]
                },
                0
              ]
            }
          },
          totalTransactions: {
            $sum: {
              $cond: [
                {
                  $or: [
                    // Payments received after session date
                    {
                      $and: [
                        {
                          $in: [
                            "$status",
                            [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                          ]
                        },
                        { $gt: ["$paymentDate", "$sessionDate"] }
                      ]
                    },
                    // Payments received before session date (advance)
                    {
                      $and: [
                        {
                          $in: [
                            "$status",
                            [PaymentTrackerStatusEnum.Paid_On_Time, PaymentTrackerStatusEnum.Paid_Delayed]
                          ]
                        },
                        { $lt: ["$paymentDate", "$sessionDate"] }
                      ]
                    },
                    // Cancellation fees
                    { $eq: ["$status", PaymentTrackerStatusEnum.Cancelled_Paid_Fee] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          receivedAfterSessions: {
            amount: "$receivedAfterSessionsAmount",
            count: "$receivedAfterSessionsCount",
            percentage: {
              amount: {
                $cond: [
                  { $eq: ["$totalAmount", 0] },
                  0,
                  {
                    $round: [
                      {
                        $multiply: [
                          { $divide: ["$receivedAfterSessionsAmount", "$totalAmount"] },
                          100
                        ]
                      },
                      2
                    ]
                  }
                ]
              },
            },
          },
          cancellationFees: {
            amount: "$cancellationFeesAmount",
            count: "$cancellationFeesCount",
            percentage: {
              amount: {
                $cond: [
                  { $eq: ["$totalAmount", 0] },
                  0,
                  {
                    $round: [
                      {
                        $multiply: [
                          { $divide: ["$cancellationFeesAmount", "$totalAmount"] },
                          100
                        ]
                      },
                      2
                    ]
                  }
                ]
              },
            },
          },
          advancePayments: {
            amount: "$advancePaymentAmount",
            count: "$advancePaymentCount",
            percentage: {
              amount: {
                $cond: [
                  { $eq: ["$totalAmount", 0] },
                  0,
                  {
                    $round: [
                      {
                        $multiply: [
                          { $divide: ["$advancePaymentAmount", "$totalAmount"] },
                          100
                        ]
                      },
                      2
                    ]
                  }
                ]
              },
            },
          },
          summary: {
            totalAmount: "$totalAmount",
            totalTransactions: "$totalTransactions"
          }
        }
      }
    ];

    const results = await PayTrackerModel.aggregate(pipeline);

    // Return the first result or default values if no data
    return results.length > 0 ? results[0] : {
      receivedAfterSessions: {
        amount: 0,
        count: 0,
        percentage: { amount: 0 }
      },
      cancellationFees: {
        amount: 0,
        count: 0,
        percentage: { amount: 0 }
      },
      advancePayments: {
        amount: 0,
        count: 0,
        percentage: { amount: 0 }
      },
      summary: { totalAmount: 0, totalTransactions: 0 }
    };
  }
}
