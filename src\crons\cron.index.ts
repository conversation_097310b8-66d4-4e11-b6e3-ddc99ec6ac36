import { Calendar<PERSON>ron } from "./calendar.cron";
import { MarkSchedueCron } from "./markScheduleCompleted.cron";
import { SendEmailCron } from "./sendEmail.cron";

export class Cron{
    // static start(){
    //     console.log('\x1b[36m%s\x1b[0m', "Syncing Calendars");
        // CalendarCron.syncCalendar()
    // }

    static sendEmail(){
        console.log('\x1b[36m%s\x1b[0m', "Sending Email");
        SendEmailCron.sendEmail2()
    }

    static markCompleted(){
        console.log('\x1b[36m%s\x1b[0m', "Marking Complete");
        MarkSchedueCron.markScheduleCompleted()
        // MarkSchedueCron.markScheduleCancelled()
    }
    
}