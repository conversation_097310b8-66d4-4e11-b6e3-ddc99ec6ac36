import { ScheduleDao } from "../lib/dao/schedule.dao";
import { GoogleCalendarService } from "./googleCalendar.service";
import { isOverlap, findOverlaps } from "../helper/custom.helper";
import moment from "moment";

/**
 * Service to check for conflicts when rescheduling therapy sessions
 */
export class RescheduleConflictService {
  /**
   * Check for conflicts when rescheduling a single therapy session
   *
   * @param therapistId The ID of the therapist
   * @param newDate The new date for the session (ISO date string)
   * @param duration Duration of the session in minutes
   * @returns Object with conflict information
   */
  static async checkSingleSessionConflict(
    therapistId: string,
    newDate: string,
    duration: number
  ) {
    try {
      // Convert newDate to Date object
      const fromDate = moment(newDate);
      const toDate = moment(newDate).add(duration, 'minutes');

      // Create the requested date object
      const requestedRecurrenceDate = {
        fromDate: fromDate.toDate(),
        toDate: toDate.toDate()
      };

      // Get all existing sessions for the therapist
      const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);

      // Extract all recurrence dates from schedules with client information
      let existingDates: any[] = [];
      for (let schedule of schedules) {
        // Filter out cancelled sessions
        const activeDates = schedule.recurrenceDates.filter(
          (date: any) => date.status !== 'cancelled'
        );

        // Add client information to each active date
        const datesWithClientInfo = activeDates.map((date: any) => ({
          ...date,
          clientName: (schedule.clientId && typeof schedule.clientId === 'object' && (schedule.clientId as any).name)
            ? (schedule.clientId as any).name
            : schedule.name || 'Unknown Client',
          scheduleName: schedule.name
        }));

        existingDates.push(...datesWithClientInfo);
      }

      // Check for conflicts in database
      const dbConflicts: { startTime: string; endTime: string; clientName?: string }[] = [];
      const checkDbOverlap = isOverlap([requestedRecurrenceDate], existingDates);

      if (checkDbOverlap) {
        // Find all overlapping dates with client information
        dbConflicts.push(...findOverlaps([requestedRecurrenceDate], existingDates));
      }

      // Check for conflicts in Google Calendar
      const calendarConflicts: { startTime: string; endTime: string; clientName?: string }[] = [];
      const events = await GoogleCalendarService.eventByDate(
        therapistId,
        200,
        fromDate.toDate(),
        toDate.toDate()
      );

      if (events && events.length > 0) {
        for (const event of events) {
          calendarConflicts.push({
            startTime: event.start.dateTime,
            endTime: event.end.dateTime,
            clientName: event.summary || 'Calendar Event'
          });
        }
      }

      // Combine all conflicts
      const allConflicts = [...dbConflicts, ...calendarConflicts];

      // Remove duplicates (if any)
      const uniqueConflicts = allConflicts.filter((conflict, index, self) =>
        index === self.findIndex((c) =>
          c.startTime === conflict.startTime && c.endTime === conflict.endTime
        )
      );

      // Format response
      if (uniqueConflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = uniqueConflicts[0];
        const formattedDate = moment(firstConflict.startTime).format('YYYY-MM-DD HH:mm');

        // Create user-friendly conflict dates array
        const formattedConflictDates = uniqueConflicts.map(conflict => {
          const startTime = moment(conflict.startTime).format('YYYY-MM-DD HH:mm');
          const endTime = moment(conflict.endTime).format('HH:mm');
          return `${startTime} - ${endTime}`;
        });

        // Create message that matches the format used in the sync process
        let message = "";
        if (uniqueConflicts.length === 1) {
          message = `A conflict was found. You have a session on ${formattedDate}.`;
        } else {
          message = `A conflict was found. You have ${uniqueConflicts.length} existing sessions in your calendar.`;
          message += ` For example, you have a session on ${formattedDate}.`;
        }

        return {
          success: false,
          message: message,
          conflicts: uniqueConflicts,
          totalConflicts: uniqueConflicts.length,
          hasConflicts: true,
          conflictDates: formattedConflictDates
        };
      }

      return {
        success: true,
        message: "No conflicts found.",
        conflicts: [],
        totalConflicts: 0,
        hasConflicts: false,
        conflictDates: []
      };
    } catch (error) {
      console.error("Error checking for single session conflicts:", error);
      return {
        success: false,
        message: "Error checking for conflicts.",
        conflicts: [],
        totalConflicts: 0,
        hasConflicts: false,
        conflictDates: []
      };
    }
  }

  /**
   * Check for conflicts when rescheduling an entire series of therapy sessions
   *
   * @param therapistId The ID of the therapist
   * @param newStartDate The new start date for the series (ISO date string)
   * @param recurrencePattern The recurrence pattern or list of dates
   * @param duration Duration of each session in minutes
   * @returns Object with conflict information
   */
  static async checkEntireSeriesConflict(
    therapistId: string,
    newStartDate: string,
    recurrencePattern: any,
    duration: number
  ) {
    try {
      // Generate all future dates in the recurring series
      const futureDates = this.generateFutureDates(newStartDate, recurrencePattern, duration);

      // Get all existing sessions for the therapist
      const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);

      // Extract all recurrence dates from schedules with client information
      let existingDates: any[] = [];
      for (let schedule of schedules) {
        // Filter out cancelled sessions
        const activeDates = schedule.recurrenceDates.filter(
          (date: any) => date.status !== 'cancelled'
        );

        // Add client information to each active date
        const datesWithClientInfo = activeDates.map((date: any) => ({
          ...date,
          clientName: (schedule.clientId && typeof schedule.clientId === 'object' && (schedule.clientId as any).name)
            ? (schedule.clientId as any).name
            : schedule.name || 'Unknown Client',
          scheduleName: schedule.name
        }));

        existingDates.push(...datesWithClientInfo);
      }

      // Check for conflicts in database
      const dbConflicts: { startTime: string; endTime: string; clientName?: string }[] = [];

      // Check each future date individually against all existing dates
      for (const futureDate of futureDates) {
        // Check if this specific date conflicts with any existing date
        for (const existingDate of existingDates) {
          // Check for overlap between this future date and existing date
          const futureStart = moment(futureDate.fromDate);
          const futureEnd = moment(futureDate.toDate);
          const existingStart = moment(existingDate.fromDate);
          const existingEnd = moment(existingDate.toDate);

          // Check for overlap
          if (
            (futureStart.isAfter(existingStart) && futureStart.isBefore(existingEnd)) || // futureStart inside existing range
            (futureEnd.isAfter(existingStart) && futureEnd.isBefore(existingEnd)) || // futureEnd inside existing range
            (existingStart.isAfter(futureStart) && existingStart.isBefore(futureEnd)) || // existingStart inside future range
            (existingEnd.isAfter(futureStart) && existingEnd.isBefore(futureEnd)) || // existingEnd inside future range
            (futureStart.isSame(existingStart) && futureEnd.isSame(existingEnd)) // Exact match case
          ) {
            // Add this conflict
            dbConflicts.push({
              startTime: existingDate.fromDate.toISOString(),
              endTime: existingDate.toDate.toISOString(),
              clientName: existingDate.clientName || 'Unknown Client'
            });

          }
        }
      }

      // Check for conflicts in Google Calendar
      const calendarConflicts: { startTime: string; endTime: string; clientName?: string }[] = [];

      // Check each future date against Google Calendar
      for (const date of futureDates) {

        const events = await GoogleCalendarService.eventByDate(
          therapistId,
          200,
          date.fromDate,
          date.toDate
        );

        if (events && events.length > 0) {

          for (const event of events) {
            calendarConflicts.push({
              startTime: event.start.dateTime,
              endTime: event.end.dateTime,
              clientName: event.summary || 'Calendar Event'
            });
          }
        }
      }

      // Combine all conflicts
      const allConflicts = [...dbConflicts, ...calendarConflicts];

      // Remove duplicates (if any)
      const uniqueConflicts = allConflicts.filter((conflict, index, self) =>
        index === self.findIndex((c) =>
          c.startTime === conflict.startTime && c.endTime === conflict.endTime
        )
      );

      // Format response
      if (uniqueConflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = uniqueConflicts[0];
        const formattedDate = moment(firstConflict.startTime).format('YYYY-MM-DD HH:mm');

        // Create user-friendly conflict dates array
        const formattedConflictDates = uniqueConflicts.map(conflict => {
          const startTime = moment(conflict.startTime).format('YYYY-MM-DD HH:mm');
          const endTime = moment(conflict.endTime).format('HH:mm');
          return `${startTime} - ${endTime}`;
        });

        // Create message that matches the format used in the sync process
        let message = "";
        if (uniqueConflicts.length === 1) {
          message = `A conflict was found. You have a session on ${formattedDate}.`;
        } else {
          message = `A conflict was found. You have ${uniqueConflicts.length} existing sessions in your calendar.`;
          message += ` For example, you have a session on ${formattedDate}.`;
        }

        return {
          success: false,
          message: message,
          conflicts: uniqueConflicts,
          totalConflicts: uniqueConflicts.length,
          hasConflicts: true,
          conflictDates: formattedConflictDates
        };
      }

      return {
        success: true,
        message: "No conflicts found.",
        conflicts: [],
        totalConflicts: 0,
        hasConflicts: false,
        conflictDates: []
      };
    } catch (error) {
      console.error("Error checking for series conflicts:", error);
      return {
        success: false,
        message: "Error checking for conflicts.",
        conflicts: [],
        totalConflicts: 0,
        hasConflicts: false,
        conflictDates: []
      };
    }
  }

  /**
   * Generate future dates based on recurrence pattern
   *
   * @param startDate The start date for the series
   * @param recurrencePattern The recurrence pattern or list of dates
   * @param duration Duration of each session in minutes
   * @returns Array of date objects with fromDate and toDate
   */
  private static generateFutureDates(
    startDate: string,
    recurrencePattern: any,
    duration: number
  ): Array<{ fromDate: Date; toDate: Date }> {

    // If recurrencePattern is an array of dates, use it directly
    if (Array.isArray(recurrencePattern)) {
      return recurrencePattern.map(date => {
        const fromDate = new Date(date);
        const toDate = new Date(fromDate);
        toDate.setMinutes(toDate.getMinutes() + duration);
        return { fromDate, toDate };
      });
    }

    // Otherwise, generate dates based on the pattern
    const dates: Array<{ fromDate: Date; toDate: Date }> = [];
    const fromDate = new Date(startDate);
    const toDate = new Date(fromDate);
    toDate.setMinutes(toDate.getMinutes() + duration);

    // Add the first date
    dates.push({ fromDate: new Date(fromDate), toDate: new Date(toDate) });

    // If recurrencePattern is a string
    if (typeof recurrencePattern === 'string') {

      // Handle different recurrence patterns
      if (recurrencePattern.includes('Does Not Repeat')) {

        // Only one date for non-recurring events
        return dates;
      }

      // For recurring events, generate future dates
      const count = 12; // Generate 12 occurrences by default (about 3 months for weekly)

      // Determine interval based on pattern
      let interval = 7; // Default to weekly
      let dayOfWeek = -1; // Default to same day of week

      if (recurrencePattern.includes('Every Day')) {
        interval = 1; // Daily
      } else if (recurrencePattern.includes('Two Weeks')) {
        interval = 14; // Bi-weekly
      } else {
        // Check for specific day of week
        const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        for (let i = 0; i < days.length; i++) {
          if (recurrencePattern.includes(days[i])) {
            dayOfWeek = i + 1; // 1 = Monday, 7 = Sunday
            break;
          }
        }
      }

      // Generate dates
      for (let i = 1; i < count; i++) {
        let nextFromDate = new Date(fromDate);

        if (dayOfWeek > 0) {
          // If specific day of week is specified, find the next occurrence of that day
          nextFromDate.setDate(nextFromDate.getDate() + (i * 7)); // Move forward by weeks

          // Adjust to the correct day of week if needed
          const currentDayOfWeek = nextFromDate.getDay() || 7; // Convert 0 (Sunday) to 7
          const daysToAdd = (dayOfWeek - currentDayOfWeek + 7) % 7;
          nextFromDate.setDate(nextFromDate.getDate() + daysToAdd);
        } else {
          // Otherwise just add the interval
          nextFromDate.setDate(nextFromDate.getDate() + (i * interval));
        }

        const nextToDate = new Date(nextFromDate);
        nextToDate.setMinutes(nextToDate.getMinutes() + duration);

        dates.push({ fromDate: nextFromDate, toDate: nextToDate });
      }
    } else if (typeof recurrencePattern === 'object') {
      // Handle object-based recurrence pattern

      const count = recurrencePattern.count || 12;
      const frequency = recurrencePattern.frequency || 'weekly';
      const interval = recurrencePattern.interval || 1;

      // Generate dates based on frequency
      for (let i = 1; i < count; i++) {
        let nextFromDate = new Date(fromDate);

        if (frequency === 'daily') {
          nextFromDate.setDate(nextFromDate.getDate() + (i * interval));
        } else if (frequency === 'weekly') {
          nextFromDate.setDate(nextFromDate.getDate() + (i * 7 * interval));
        } else if (frequency === 'monthly') {
          nextFromDate.setMonth(nextFromDate.getMonth() + (i * interval));
        } else if (frequency === 'yearly') {
          nextFromDate.setFullYear(nextFromDate.getFullYear() + (i * interval));
        }

        const nextToDate = new Date(nextFromDate);
        nextToDate.setMinutes(nextToDate.getMinutes() + duration);

        dates.push({ fromDate: nextFromDate, toDate: nextToDate });
      }
    }

    return dates;
  }
}