import PayTrackerModel, {
  CurrencyEnum,
  PaymentTrackerStatusEnum,
} from "../../models/PayTracker.model";

export class PayTrackerDao {
  static async getAllPayTracker(pageSize: any, skip: any) {
    return await PayTrackerModel.find({ isDeleted: false })
      .skip(skip)
      .limit(pageSize);
  }

  static async countPayTracker() {
    return await PayTrackerModel.find({ isDeleted: false }).countDocuments();
  }

  static async getPayTrackerByTherapistId(query: any) {
    return await PayTrackerModel.find(query)
      .populate({
        path: "clientId",
        select: "name email age gender isActive clientId",
      })
      .populate({
        path: "scheduleId",
        select:
          "createdAt updatedAt recurrenceDates.fromDate recurrenceDates._id recurrenceDates.status",
      })
      .lean();
  }

  // static async isActive(allPayTracker: any) {
  //   return allPayTracker.filter((item: any) => item.clientId.isActive == true);
  // }

  //   static async getPayTrackerByTherapistId(query: any, searchText?: string) {
  //     // Add the optional search filter for clientId.name
  //     if (searchText) {
  //       query["clientId.name"] = { $regex: searchText, $options: "i" }; // Case-insensitive search
  //     }

  //     return await PayTrackerModel.find(query)
  //       .populate({
  //         path: "clientId",
  //         select: "name email", // populate name and email fields from clientId
  //       })
  //       .populate({
  //         path: "scheduleId",
  //         select:
  //           "createdAt updatedAt recurrenceDates.fromDate recurrenceDates._id recurrenceDates.status",
  //       })
  //       .lean();
  //   }

  static async countPayTrackerByTherapistId(therapistId: any) {
    return await PayTrackerModel.find({
      therapistId: therapistId,
      isDeleted: false,
    }).countDocuments();
  }

  static async createPayTracker(payload: any) {
    return await PayTrackerModel.create(payload);
  }

  static async updatePayTracker(id: any, payload: any) {
    return await PayTrackerModel.findByIdAndUpdate(
      { _id: id },
      {
        $set: payload,
      },
      { new: true }
    );
  }

  static async deletePayTracker(id: any) {
    return await PayTrackerModel.findByIdAndUpdate(
      { _id: id },
      {
        $set: { isDeleted: true },
      },
      { new: true }
    );
  }

  static async deletePayTrackersByScheduleRecIds(scheduleRecIds: string[]) {
    return await PayTrackerModel.deleteMany({
      scheduleRecId: { $in: scheduleRecIds },
    });
  }

  static async changePayTrackerStatus(id: any, status: any) {
    let payload: any = {
      status: status,
    };
    if (
      status == PaymentTrackerStatusEnum.Paid_On_Time ||
      status == PaymentTrackerStatusEnum.Paid_Delayed
    ) {
      payload["paymentDate"] = new Date();
    }
    if (status == PaymentTrackerStatusEnum.Cancelled_Zero_Fee) {
      payload["cancellationFee"] = {
        currency: CurrencyEnum.INR,
        value: 0
      };
    }
    return await PayTrackerModel.findByIdAndUpdate(
      { _id: id },
      {
        $set: payload,
      },
      { new: true }
    );
  }

  static async getStats(query: any) {
    return await PayTrackerModel.find(query);
  }

  static async getTherapistClientsByName(id: any, name: any) {
    return await PayTrackerModel.find({ therapistId: id }).populate({
      path: "clientId",
      match: { name: { $regex: name, $options: "i" } },
      select: "name",
    });
  }

  static async getPayTrackerByTherapistIdPipeline(pipeline: any) {
    return await PayTrackerModel.aggregate(pipeline);
  }

  static async getpaytrackerById(id: any) {
    return await PayTrackerModel.findById(id);
  }

  static async getpaytrackerByTherapistandId(id: any, therapistId: any) {
    return await PayTrackerModel.findOne({ _id: id, therapistId: therapistId });
  }

  static async getPaytrackerByScheduleIdAndScheduleRecId(
    scheduleId: any,
    scheduleRecId: any
  ) {
    return await PayTrackerModel.findOne({
      scheduleId: scheduleId,
      scheduleRecId: scheduleRecId,
    });
  }

  static async updatePayTrackerAmountWithFlag(id: any, therapistId: any, amount: any) {
    return await PayTrackerModel.findOneAndUpdate(
      {
        _id: id,
        therapistId: therapistId
      },
      {
        $set: {
          "amount.value": amount,
          publicCalendarAmountUpdated: true
        }
      },
      { new: true }
    );
  }
}
