import { ClientAnalyticsDao } from "../lib/dao/clientAnalytics.dao";
import { ScheduleStatus } from "../models/Schedule.model";

export class ClientAnalyticsService {
  /**
   * Get highest cancellation day of the week based on cancelled sessions
   * @param therapistId - Optional therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of highest cancellation days
   */
  static async getHighestCancellationDay(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
    }
  ) {
    // Get aggregated cancellation data from DAO
    const aggregatedData = await ClientAnalyticsDao.aggregateCancellationsByDayOfWeek(
      therapistId,
      options?.dateRange
    );

    // Format the results
    const formattedDays = this.formatCancellationDays(aggregatedData);
    const daysWithPercentages = this.calculateCancellationPercentages(formattedDays);

    return {
      highestCancellationDays: daysWithPercentages,
      totalCancellations: daysWithPercentages.reduce(
        (sum, day) => sum + day.totalCancellations,
        0
      ),
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }

  /**
   * Format aggregated cancellation data with day names
   * @param aggregatedData - Raw aggregated cancellation data from database
   * @returns Formatted array with day names and cancellation counts
   */
  private static formatCancellationDays(aggregatedData: any[]) {
    const dayNames = {
      1: "Sunday",
      2: "Monday",
      3: "Tuesday",
      4: "Wednesday",
      5: "Thursday",
      6: "Friday",
      7: "Saturday",
    };

    return aggregatedData.map((day) => ({
      dayOfWeek: dayNames[day._id as keyof typeof dayNames],
      dayNumber: day._id,
      totalCancellations: day.totalCancellations,
      percentage: 0, // Will be calculated in next step
    }));
  }

  /**
   * Calculate percentage share for each cancellation day
   * @param formattedDays - Formatted cancellation days array
   * @returns Array with percentage calculations
   */
  private static calculateCancellationPercentages(formattedDays: any[]) {
    const totalCancellations = formattedDays.reduce(
      (sum, day) => sum + day.totalCancellations,
      0
    );

    const daysWithPercentages = formattedDays.map((day) => ({
      ...day,
      percentage:
        totalCancellations > 0
          ? parseFloat(((day.totalCancellations / totalCancellations) * 100).toFixed(1))
          : 0,
    }));

    // Sort by total cancellations in descending order
    return daysWithPercentages.sort((a, b) => b.totalCancellations - a.totalCancellations);
  }

  /**
   * Get busiest days of the week based on session scheduling patterns
   * @param therapistId - Optional therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of busiest days with session counts and percentages
   */
  static async getBusiestDaysOfWeek(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
      includeStatuses?: ScheduleStatus[];
    }
  ) {
    const defaultStatuses = [ScheduleStatus.CONFIRMED, ScheduleStatus.COMPLETED, ScheduleStatus.RESCHEDULED];
    const statuses = options?.includeStatuses || defaultStatuses;

    // Get aggregated data from DAO
    const aggregatedData = await ClientAnalyticsDao.aggregateSessionsByDayOfWeek(
      therapistId,
      options?.dateRange,
      statuses
    );

    // Format the results
    const formattedDays = this.formatBusiestDays(aggregatedData);
    const daysWithPercentages = this.calculatePercentages(formattedDays);

    return {
      busiestDays: daysWithPercentages,
      totalSessions: daysWithPercentages.reduce(
        (sum, day) => sum + day.sessionCount,
        0
      ),
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }

  /**
   * Format aggregated day data with day names
   * @param aggregatedData - Raw aggregated data from database
   * @returns Formatted array with day names and session counts
   */
  private static formatBusiestDays(aggregatedData: any[]) {
    const dayNames = {
      1: "Sunday",
      2: "Monday",
      3: "Tuesday",
      4: "Wednesday",
      5: "Thursday",
      6: "Friday",
      7: "Saturday",
    };

    return aggregatedData.map((day) => ({
      dayOfWeek: dayNames[day._id as keyof typeof dayNames],
      dayNumber: day._id,
      sessionCount: day.sessionCount,
      percentage: 0, // Will be calculated in next step
    }));
  }

  /**
   * Calculate percentage share for each day
   * @param formattedDays - Formatted days array
   * @returns Array with percentage calculations
   */
  private static calculatePercentages(formattedDays: any[]) {
    const totalSessions = formattedDays.reduce(
      (sum, day) => sum + day.sessionCount,
      0
    );

    return formattedDays.map((day) => ({
      ...day,
      percentage:
        totalSessions > 0
          ? parseFloat(((day.sessionCount / totalSessions) * 100).toFixed(1))
          : 0,
    }));
  }



  static async getSessionPriceAvg(therapistId: string, fromDate: string, toDate: string) {
    return await ClientAnalyticsDao.getSessionPriceAvg(therapistId, fromDate, toDate);
  }

  /**
   * Get active clients analytics (clients with ≥3 sessions in specified period)
   * @param therapistId - Optional therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of active clients with session counts and details
   */
  static async getActiveClients(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
      minSessions?: number;
    }
  ) {
    const minSessions = options?.minSessions || 3;
    // Get data from DAO
    const data = await ClientAnalyticsDao.getActiveClients(
      therapistId,
      options?.dateRange,
      minSessions
    );
    return {
      activeClients: data.activeClients,
      totalActiveClients: data.totalActiveClients,
      minSessionsThreshold: data.minSessionsThreshold,
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }

  /**
   * Get total working hours analytics from completed sessions
   * @param therapistId - Optional therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of total working hours from completed sessions
   */
  static async getTotalWorkingHours(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
    }
  ) {
    // Get data from DAO
    const data = await ClientAnalyticsDao.getTotalWorkingHours(
      therapistId,
      options?.dateRange
    );

    return {
      therapistId: data.therapistId,
      workingHours: data.workingHours,
      sessionCount: data.sessionCount,
    };
  }

  static async getPowerClients(therapistId: string, fromDate: string, toDate: string) {
    return await ClientAnalyticsDao.getPowerClients(therapistId, fromDate, toDate);
  }

  /**
   * Get risk clients analytics (clients with bad session management or payment history)
   * @param therapistId - Therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of risk clients with session and payment issues
   */
  static async getRiskClients(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
    }
  ) {
    // Get data from DAO
    const data = await ClientAnalyticsDao.getRiskClients(
      therapistId,
      options?.dateRange
    );

    // Calculate risk category counts
    let rescheduleRisk = 0;
    let cancellationRisk = 0;
    let delayedPaymentRisk = 0;
    let cancelledPaymentRisk = 0;
    const uniqueClientIds = new Set();

    for (const client of data) {
      if (client.isRescheduleRisk) rescheduleRisk++;
      if (client.isCancellationRisk) cancellationRisk++;
      if (client.isDelayedPaymentRisk) delayedPaymentRisk++;
      if (client.isCancelledPaymentRisk) cancelledPaymentRisk++;
      uniqueClientIds.add(String(client.clientId));
    }

    return {
      totalRiskClients: uniqueClientIds.size,
      riskClients: data,
      riskCategoryCounts: {
        "Reschedule Risk (>50%)": rescheduleRisk,
        "Cancellation Risk (>50%)": cancellationRisk,
        "Delayed Payment Risk (>50%)": delayedPaymentRisk,
        "Cancelled Payment Risk (>50%)": cancelledPaymentRisk,
      },
      criteria: "Clients with >50% in any of the 4 risk categories",
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }

  /**
   * Get busiest 3-hour time slots analytics
   * @param therapistId - Therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of busiest 3-hour time slots with session counts
   */
  static async getBusiestTimeSlots(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
    }
  ) {
    // Get data from DAO
    const data = await ClientAnalyticsDao.getBusiestTimeSlots(
      therapistId,
      options?.dateRange
    );

    return {
      busiestTimeSlots: data,
      totalTimeSlots: data.length,
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }

  /**
   * Get payment summary analytics from PayTracker collection
   * @param therapistId - Therapist ID for therapist-specific analysis
   * @param options - Configuration options for the analysis
   * @returns Analysis of payment data by type and status
   */
  static async getPaymentSummary(
    therapistId?: string,
    options?: {
      dateRange?: { from: Date; to: Date };
    }
  ) {
    // Get data from DAO
    const data = await ClientAnalyticsDao.getPaymentSummary(
      therapistId,
      options?.dateRange
    );

    return {
      paymentSummary: data,
      analysisRange: options?.dateRange
        ? `${options.dateRange.from.toISOString().split('T')[0]} to ${options.dateRange.to.toISOString().split('T')[0]}`
        : "All time",
      generatedAt: new Date(),
    };
  }
}
