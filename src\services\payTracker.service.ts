import moment from "moment";
import { PayTrackerDao } from "../lib/dao/payTracker.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import ScheduleModel from "../models/Schedule.model";
import PayTrackerModel, {
  PaymentTrackerStatusEnum,
} from "../models/PayTracker.model";
import mongoose from "mongoose";

export class PayTrackerService {
  static async getpaytrackerById(id: any) {
    return await PayTrackerDao.getpaytrackerById(id);
  }

  static async getpaytrackerByTherapistandId(id: any, therapistId: any) {
    return await PayTrackerDao.getpaytrackerByTherapistandId(id, therapistId);
  }

  static async getAllPayTracker(pageSize: any, skip: any) {
    return await PayTrackerDao.getAllPayTracker(pageSize, skip);
  }

  static async countPayTracker() {
    return await PayTrackerDao.countPayTracker();
  }

  static async getPayTrackerByTherapistId(query: any) {
    return await PayTrackerDao.getPayTrackerByTherapistId(query);
  }

  static async countPayTrackerByTherapistId(id: any) {
    return await PayTrackerDao.countPayTrackerByTherapistId(id);
  }

  static async createPayTracker(payload: any) {
    return await PayTrackerDao.createPayTracker(payload);
  }

  static async updatePayTracker(id: any, payload: any) {
    return await PayTrackerDao.updatePayTracker(id, payload);
  }

  static async deletePayTracker(id: any) {
    return await PayTrackerDao.deletePayTracker(id);
  }

  static async getTagsByScheduleId(id: any) {
    const response: any = await ScheduleDao.getTherapistClientByScheduleId(id);
    const tags = [];
    tags.push(response?.name);
    tags.push(response?.clientId.name);
    tags.push(response?.therapistId?.name);
    return tags;
  }

  static async changePayTrackerStatus(id: any, status: any) {
    return await PayTrackerDao.changePayTrackerStatus(id, status);
  }

  static async getStats(query: any) {
    return await PayTrackerDao.getStats(query);
  }

  static async getTherapistClientsByName(id: any, name: any) {
    return await PayTrackerDao.getTherapistClientsByName(id, name);
  }

  static async getPayTrackerByTherapistIdPipeline(pipeline: any) {
    return await PayTrackerDao.getPayTrackerByTherapistIdPipeline(pipeline);
  }

  static async getPaytrackerByScheduleIdAndScheduleRecId(
    scheduleId: any,
    scheduleRecId: any
  ) {
    return await PayTrackerDao.getPaytrackerByScheduleIdAndScheduleRecId(
      scheduleId,
      scheduleRecId
    );
  }

  static async getStatsData(therapistId: any, startDate: any, endDate: any) {
    let stats = {
      // clients: 0,
      // completed_session: 0,
      // cancelled_session: 0,
      collected_payment: 0,
      pending_payment: 0,
      total_earnings: 0,
    };

    // const sessions = await ScheduleModel.find({ therapistId: therapistId, 'recurrenceDates.fromDate': { $gte: new Date(startDate), $lte: new Date(endDate) } }, '_id recurrenceDates');

    // let reccurances = []

    // for (let session of sessions) {
    //     for (let recurrence of session.recurrenceDates) {
    //         if (moment(recurrence.fromDate).isBetween(startDate, endDate)) {
    //             reccurances.push(recurrence._id)
    //         }
    //     }
    // }

    // const paytrackers = await PayTrackerModel.find({ therapistId: therapistId, scheduleRecId: { $in: reccurances }, linkExist: { $ne: true } });
    const [paytrackers] = await PayTrackerModel.aggregate([
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId),
          linkExist: { $ne: true },
        },
      },
      {
        $facet: {
          paid: [
            {
              $match: {
                status: {
                  $in: [
                    PaymentTrackerStatusEnum.Paid_On_Time,
                    PaymentTrackerStatusEnum.Paid_Delayed,
                  ],
                },
                paymentDate: {
                  $gte: new Date(startDate),
                  $lte: new Date(endDate),
                },
              },
            },
            {
              $group: {
                _id: null,
                collected_payment: { $sum: "$amount.value" },
                total_earnings: { $sum: "$amount.value" },
              },
            },
            { $project: { _id: 0, collected_payment: 1, total_earnings: 1 } },
          ],
          cancel: [
            {
              $match: {
                status: PaymentTrackerStatusEnum.Cancelled_Paid_Fee,
                updatedAt: {
                  $gte: new Date(startDate),
                  $lte: new Date(endDate),
                },
              },
            },
            {
              $group: {
                _id: null,
                collected_payment: { $sum: "$cancellationFee.value" },
                total_earnings: { $sum: "$cancellationFee.value" },
              },
            },
            { $project: { _id: 0, collected_payment: 1, total_earnings: 1 } },
          ],
          pending: [
            {
              $match: {
                status: PaymentTrackerStatusEnum.Still_Pending,
                updatedAt: {
                  $gte: new Date(startDate),
                  $lte: new Date(endDate),
                },
              },
            },
            {
              $group: {
                _id: null,
                pending_payment: { $sum: "$amount.value" },
              },
            },
            { $project: { _id: 0, pending_payment: 1 } },
          ],
        },
      },
      {
        $unwind: {
          path: "$paid",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$cancel",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$pending",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          collected_payment: {
            $sum: ["$paid.collected_payment", "$cancel.collected_payment"],
          },
          total_earnings: {
            $sum: ["$paid.total_earnings", "$cancel.total_earnings"],
          },
          pending_payment: "$pending.pending_payment",
        },
      },
    ]);
    // Cal Clients
    // const all_clients = paytrackers.map((data) => String(data.clientId));
    // const unique_clients = Array.from(new Set(all_clients));
    // stats.clients = unique_clients.length;

    // Cal Completed Sessions

    // const completed_sessions = paytrackers.filter((data) => (data.status == PaymentTrackerStatusEnum.Paid_On_Time) || (data.status == PaymentTrackerStatusEnum.Paid_Delayed)).length;

    // stats.completed_session = completed_sessions;

    // Cal Cancelled Sessions
    // const cancelled_sessions = paytrackers.filter((data) => (data.status == PaymentTrackerStatusEnum.Cancelled_Paid_Fee) || (data.status == PaymentTrackerStatusEnum.Cancelled_Zero_Fee)).length;
    // stats.cancelled_session = cancelled_sessions;

    // Cal Collected Payment

    // for (const paytracker of paytrackers) {
    //     if (paytracker.status == PaymentTrackerStatusEnum.Paid_On_Time || paytracker.status == PaymentTrackerStatusEnum.Paid_Delayed) {
    //         stats.collected_payment += paytracker.amount.value;
    //         stats.total_earnings += paytracker.amount.value;
    //     }
    //     if (paytracker.status == PaymentTrackerStatusEnum.Cancelled_Paid_Fee) {
    //         stats.collected_payment += paytracker.cancellationFee.value;
    //         stats.total_earnings += paytracker.cancellationFee.value;
    //     }
    //     if (paytracker.status == PaymentTrackerStatusEnum.Still_Pending) {
    //         console.log('---')
    //         stats.pending_payment += paytracker.amount.value;
    //     }
    // }

    return paytrackers;
  }

  static async updatePayTrackerAmountWithFlag(id: any, therapistId: any, amount: any) {
    return await PayTrackerDao.updatePayTrackerAmountWithFlag(id, therapistId, amount);
  }
}
